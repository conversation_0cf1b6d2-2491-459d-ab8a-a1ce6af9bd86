<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> <PERSON></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* --- Base and Layout --- */
        :root {
            --bg-color: #1a1a1a;
            --surface-color: #2c2c2c;
            --primary-color: #8a4fff;
            --primary-hover-color: #a070ff;
            --text-color: #f0f0f0;
            --subtle-text-color: #a0a0a0;
            --border-color: #444444;
            --grid-color: rgba(255, 255, 255, 0.05);
            --shadow-color: rgba(0, 0, 0, 0.4);
            --success-color: #28a745;
            --error-color: #dc3545;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            display: flex;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            flex-grow: 1;
        }

        /* --- Node Library (Sidebar) --- */
        .node-library {
            width: 240px;
            background-color: var(--surface-color);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 5px 0 15px var(--shadow-color);
            z-index: 10;
        }
        
        .sidebar-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .view-toggle {
            display: flex;
            margin-bottom: 20px;
            border-radius: 8px;
            background-color: #3a3a3a;
            padding: 4px;
        }

        .view-toggle button {
            flex: 1;
            padding: 8px;
            border: none;
            background-color: transparent;
            color: var(--subtle-text-color);
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .view-toggle button.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 8px rgba(138, 79, 255, 0.3);
        }

        #sidebar-view-create, #sidebar-view-manage {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        
        #sidebar-view-manage {
            gap: 10px;
        }
        
        .hidden {
            display: none !important;
        }

        .node-library h2 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .secondary-btn, .styled-select, #api-key-input {
            width: 100%;
            padding: 10px;
            font-size: 0.9rem;
            font-weight: 500;
            background-color: #3a3a3a;
            color: var(--subtle-text-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .styled-select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a0a0a0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 1em;
            padding-right: 2.5em;
        }
        
        .secondary-btn:not(.styled-select) {
             background-image: none;
        }

        .secondary-btn:hover, .styled-select:hover, #api-key-input:hover {
            background-color: #4a4a4a;
            color: var(--text-color);
            border-color: var(--subtle-text-color);
        }
        #api-key-input:focus {
             outline: none;
             border-color: var(--primary-color);
        }

        .library-node {
            background-color: #3a3a3a;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: grab;
            user-select: none;
            transition: all 0.2s ease;
            border-left: 4px solid transparent;
        }

        .library-node:hover {
            background-color: #4a4a4a;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px var(--shadow-color);
        }

        .library-node h3 {
            font-size: 0.95rem;
            margin-bottom: 5px;
        }

        .library-node p {
            font-size: 0.8rem;
            color: var(--subtle-text-color);
        }
        
        #node-char { border-left-color: #4f86f7; }
        #node-plot { border-left-color: #f7a04f; }
        #node-setting { border-left-color: #5de2a5; }
        #node-style { border-left-color: #f7d54f; }
        #node-theme { border-left-color: #f74f76; }
        #node-spec { border-left-color: #20c997; }

        .generate-button-container {
            margin-top: auto;
            padding-top: 15px;
        }
        
        .api-key-section {
            border-top: 1px solid var(--border-color);
            padding-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        #generate-btn {
            width: 100%;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            background-image: linear-gradient(to right, var(--primary-color), #a070ff);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #generate-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.4);
        }

        #generate-btn:disabled {
            background: #555;
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        #generate-btn.loading {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
             0% { box-shadow: 0 0 0 0 rgba(138, 79, 255, 0.4); }
             70% { box-shadow: 0 0 0 10px rgba(138, 79, 255, 0); }
             100% { box-shadow: 0 0 0 0 rgba(138, 79, 255, 0); }
        }

        /* --- Canvas Area --- */
        .canvas-area {
            flex-grow: 1;
            position: relative;
            background-image:
                linear-gradient(var(--grid-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
            background-size: 20px 20px;
            overflow: hidden;
        }

        #canvas {
            width: 100%;
            height: 100%;
            position: relative;
        }

        #connector-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .connector-line {
            stroke: var(--primary-color);
            stroke-width: 3px;
            fill: none;
            animation: draw-line 0.5s ease-out;
            pointer-events: stroke;
            cursor: pointer;
            transition: stroke 0.2s ease;
        }

        .connector-line:hover {
             stroke: var(--primary-hover-color);
        }
        
        @keyframes draw-line {
            from { stroke-dasharray: 1000; stroke-dashoffset: 1000; }
            to { stroke-dasharray: 1000; stroke-dashoffset: 0; }
        }

        .canvas-node {
            position: absolute;
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            width: 220px;
            min-height: 80px;
            box-shadow: 0 5px 20px var(--shadow-color);
            cursor: move;
            display: flex;
            flex-direction: column;
            z-index: 5;
            transition: box-shadow 0.2s, transform 0.2s;
        }
        
        .canvas-node.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 15px var(--primary-color);
            transform: scale(1.02);
        }
        
        .node-header {
            padding: 8px 12px;
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .node-header.character-header { background-color: rgba(79, 134, 247, 0.2); }
        .node-header.plot-header { background-color: rgba(247, 160, 79, 0.2); }
        .node-header.setting-header { background-color: rgba(93, 226, 165, 0.2); }
        .node-header.style-header { background-color: rgba(247, 213, 79, 0.2); }
        .node-header.theme-header { background-color: rgba(247, 79, 118, 0.2); }
        .node-header.specification-header { background-color: rgba(32, 201, 151, 0.2); }

        
        .delete-node-btn {
            background: none;
            border: none;
            color: var(--subtle-text-color);
            cursor: pointer;
            font-size: 1.2rem;
            line-height: 1;
            transition: color 0.2s;
        }

        .delete-node-btn:hover {
            color: var(--error-color);
        }

        .node-content {
            padding: 12px;
            font-size: 0.9rem;
            flex-grow: 1;
            color: var(--subtle-text-color);
        }
        
        .node-connector {
            width: 16px;
            height: 16px;
            background-color: var(--bg-color);
            border: 2px solid var(--subtle-text-color);
            border-radius: 50%;
            position: absolute;
            cursor: crosshair;
            transition: all 0.2s ease;
        }
        .node-connector:hover {
            background-color: var(--primary-color);
            border-color: white;
            transform: scale(1.2);
        }
        .node-connector.input { left: -9px; top: calc(50% - 8px); }
        .node-connector.output { right: -9px; top: calc(50% - 8px); }


        /* --- Properties Panel --- */
        .properties-panel {
            width: 320px;
            background-color: var(--surface-color);
            border-left: 1px solid var(--border-color);
            padding: 20px;
            z-index: 10;
            overflow-y: auto;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .properties-panel.hidden {
            width: 0;
            padding: 20px 0;
            overflow: hidden;
            border-left-color: transparent;
        }

        .properties-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .properties-header h2 {
            font-size: 1.2rem;
            color: var(--primary-color);
        }

        .properties-close-btn {
            background: none;
            border: none;
            color: var(--subtle-text-color);
            font-size: 1.8rem;
            cursor: pointer;
            transition: color 0.2s;
        }
        .properties-close-btn:hover {
            color: white;
        }

        #properties-form {
            flex-grow: 1;
        }
        
        .properties-load-section {
            padding-bottom: 15px;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
       
        #properties-form .form-group {
            margin-bottom: 15px;
        }

        #properties-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 0.9rem;
            color: var(--subtle-text-color);
        }
        
        #properties-form input,
        #properties-form textarea,
        .properties-load-section .styled-select {
            width: 100%;
            background-color: #3a3a3a;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 10px;
            color: var(--text-color);
            font-family: 'Inter', sans-serif;
            font-size: 0.9rem;
            transition: border-color 0.2s;
        }
        
        #properties-form input:focus,
        #properties-form textarea:focus,
        .properties-load-section .styled-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        #properties-form textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .properties-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 10px;
        }
        
        .properties-placeholder {
            text-align: center;
            padding-top: 50%;
            color: var(--subtle-text-color);
        }
        .properties-placeholder p {
            font-size: 0.9rem;
        }

        /* --- Modals --- */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }
        
        .modal-overlay.visible {
            opacity: 1;
            pointer-events: auto;
        }
        .modal-overlay.top {
            z-index: 1001;
        }

        .modal-content {
            background-color: var(--surface-color);
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px var(--shadow-color);
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }
        .modal-overlay.visible .modal-content {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .modal-header h2 {
            color: var(--primary-color);
        }

        .modal-footer {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .modal-btn {
            padding: 10px 20px;
            font-size: 0.9rem;
            font-weight: 500;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .modal-btn.primary {
            background-color: var(--primary-color);
            color: white;
        }
        .modal-btn.primary:hover {
            background-color: var(--primary-hover-color);
        }

        .modal-btn.secondary {
            background-color: #555;
            color: var(--text-color);
        }
        .modal-btn.secondary:hover {
            background-color: #666;
        }
        .modal-btn.danger {
            background-color: var(--error-color);
            color: white;
        }
        .modal-btn.danger:hover {
            background-color: #c82333;
        }


        .modal-close-btn {
            background: none;
            border: none;
            color: var(--subtle-text-color);
            font-size: 1.8rem;
            cursor: pointer;
            transition: color 0.2s;
        }
        .modal-close-btn:hover {
            color: white;
        }

        #story-output, #prompt-output, #chat-history, #management-list {
            background-color: var(--bg-color);
            flex-grow: 1;
            overflow-y: auto;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 0.95rem;
            border: 1px solid var(--border-color);
        }
        #management-list {
            padding: 10px;
        }
        .management-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        .management-item:last-child {
            border-bottom: none;
        }
        
        /* --- AI Assistant Chat --- */
        #ai-assistant-modal .modal-content {
             height: 80vh;
        }
        #chat-history {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .chat-message {
            padding: 12px;
            border-radius: 10px;
            max-width: 80%;
        }
        .chat-message.user {
            background-color: var(--primary-color);
            align-self: flex-end;
            color: white;
        }
        .chat-message.bot {
            background-color: #3a3a3a;
            align-self: flex-start;
        }
        .chat-message.bot.loading {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .chat-message.bot.loading .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--subtle-text-color);
            animation: bounce 1.4s infinite ease-in-out both;
        }
        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1.0); }
        }

        #chat-input-form {
            display: flex;
            margin-top: 20px;
            gap: 10px;
        }
        #chat-input {
            flex-grow: 1;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: #3a3a3a;
            color: var(--text-color);
            resize: none;
            overflow-y: hidden;
            font-family: 'Inter', sans-serif;
            font-size: 0.9rem;
        }
        #chat-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        #generic-prompt-modal-input {
            width: 100%;
            margin-top: 15px;
            padding: 12px;
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 6px;
        }
        #generic-prompt-modal-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .story-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            flex-direction: column;
        }
        
        .loader {
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

    </style>
</head>
<body>

    <div class="main-container">
        <!-- Sidebar with draggable node types -->
        <aside class="node-library">
            <div class="view-toggle">
                <button id="toggle-create-view" class="active">Create</button>
                <button id="toggle-manage-view">Manage</button>
            </div>
            
            <div id="sidebar-view-create" class="sidebar-content">
                <select id="grid-select" class="styled-select" style="margin-bottom: 20px;"></select>
                <div id="node-spec" class="library-node" draggable="true" data-type="Specification">
                    <h3>Specification</h3><p>Genre, format, audience.</p>
                </div>
                <div id="node-char" class="library-node" draggable="true" data-type="Character">
                    <h3>Character</h3><p>Define a person's traits.</p>
                </div>
                <div id="node-plot" class="library-node" draggable="true" data-type="Plot">
                    <h3>Plot Point</h3><p>An event or scene in the story.</p>
                </div>
                <div id="node-setting" class="library-node" draggable="true" data-type="Setting">
                    <h3>Setting</h3><p>The time and place.</p>
                </div>
                <div id="node-style" class="library-node" draggable="true" data-type="Style">
                    <h3>Style</h3><p>The narrative voice and tone.</p>
                </div>
                <div id="node-theme" class="library-node" draggable="true" data-type="Theme">
                    <h3>Theme</h3><p>The central idea or message.</p>
                </div>
                <div class="generate-button-container">
                     <button id="generate-btn">Weave Story</button>
                </div>
            </div>

            <div id="sidebar-view-manage" class="sidebar-content hidden">
                <button id="save-grid-btn" class="secondary-btn">Save Current Grid</button>
                <button id="manage-grids-btn" class="secondary-btn">Manage Saved Grids</button>
                <button id="import-grid-btn" class="secondary-btn">Import Grid from File</button>
                <button id="export-grid-btn" class="secondary-btn">Export Current Grid</button>
                <button id="import-block-btn" class="secondary-btn">Import Block from File</button>
                <button id="clear-canvas-btn" class="secondary-btn">Clear Canvas</button>

                <div class="api-key-section">
                    <button id="ai-assistant-btn" class="secondary-btn">AI Story Assistant</button>
                    <button id="view-prompt-btn" class="secondary-btn">View Compiled Prompt</button>
                    <label for="api-key-input" style="font-size: 0.8rem; color: var(--subtle-text-color); margin-top: 10px; margin-bottom: 5px;">Gemini API Key (Optional)</label>
                    <input type="password" id="api-key-input" placeholder="Enter API Key...">
                    <button id="save-api-key-btn" class="secondary-btn" style="margin-top: 5px;">Save Key</button>
                </div>
            </div>
        </aside>

        <!-- Main canvas where nodes are dropped -->
        <main class="canvas-area" id="canvas-area">
            <div id="canvas"></div>
            <svg id="connector-svg"></svg>
            <input type="file" id="import-file-input" accept=".json" style="display: none;" />
        </main>

        <!-- Panel to edit properties of the selected node -->
        <aside class="properties-panel hidden" id="properties-panel">
            <div id="properties-content">
                 <div class="properties-placeholder">
                    <p>Select a node to edit its properties.</p>
                </div>
            </div>
        </aside>
    </div>
    
    <!-- Modals -->
    <div class="modal-overlay" id="story-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Generated Story</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div id="story-output"></div>
            <div class="modal-footer">
                <button id="copy-story-btn" class="modal-btn primary">Copy Story</button>
            </div>
        </div>
    </div>
    
    <div class="modal-overlay" id="prompt-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Compiled AI Prompt</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <pre id="prompt-output"></pre>
            <div class="modal-footer">
                <button id="copy-prompt-btn" class="modal-btn primary">Copy Prompt</button>
            </div>
        </div>
    </div>

    <div class="modal-overlay" id="generic-prompt-modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2 id="generic-prompt-modal-title">Prompt</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <p id="generic-prompt-modal-text">Enter a value:</p>
            <input type="text" id="generic-prompt-modal-input" />
            <div class="modal-footer">
                <button id="generic-prompt-modal-cancel" class="modal-btn secondary">Cancel</button>
                <button id="generic-prompt-modal-confirm" class="modal-btn primary">Confirm</button>
            </div>
        </div>
    </div>
    
    <div class="modal-overlay" id="generic-confirm-modal">
         <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2 id="generic-confirm-modal-title">Confirm</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <p id="generic-confirm-modal-text">Are you sure?</p>
            <div class="modal-footer">
                <button id="generic-confirm-modal-cancel" class="modal-btn secondary">Cancel</button>
                <button id="generic-confirm-modal-confirm" class="modal-btn primary">Confirm</button>
            </div>
        </div>
    </div>
    
    <div class="modal-overlay" id="ai-assistant-modal">
         <div class="modal-content">
            <div class="modal-header">
                <h2>AI Story Assistant</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div id="chat-history"></div>
            <form id="chat-input-form">
                <textarea id="chat-input" placeholder="e.g., Create a cynical detective..." rows="1"></textarea>
                <button type="submit" id="chat-send-btn" class="modal-btn primary">Send</button>
            </form>
        </div>
    </div>

    <div class="modal-overlay" id="management-modal">
         <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h2 id="management-modal-title">Manage Items</h2>
                <button class="modal-close-btn">&times;</button>
            </div>
            <div id="management-list"></div>
        </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // --- DOM Elements ---
        const canvas = document.getElementById('canvas');
        const canvasArea = document.getElementById('canvas-area');
        const propertiesPanel = document.getElementById('properties-panel');
        const propertiesContent = document.getElementById('properties-content');
        const libraryNodes = document.querySelectorAll('.library-node');
        const connectorSvg = document.getElementById('connector-svg');
        const generateBtn = document.getElementById('generate-btn');
        const viewPromptBtn = document.getElementById('view-prompt-btn');
        
        // Modals
        const storyModal = document.getElementById('story-modal');
        const storyOutput = document.getElementById('story-output');
        const copyStoryBtn = document.getElementById('copy-story-btn');
        const promptModal = document.getElementById('prompt-modal');
        const promptOutput = document.getElementById('prompt-output');
        const copyPromptBtn = document.getElementById('copy-prompt-btn');
        const aiAssistantModal = document.getElementById('ai-assistant-modal');
        const chatHistory = document.getElementById('chat-history');
        const chatInputForm = document.getElementById('chat-input-form');
        const chatInput = document.getElementById('chat-input');
        const managementModal = document.getElementById('management-modal');


        // Buttons & Inputs
        const gridSelect = document.getElementById('grid-select');
        const saveGridBtn = document.getElementById('save-grid-btn');
        const manageGridsBtn = document.getElementById('manage-grids-btn');
        const importGridBtn = document.getElementById('import-grid-btn');
        const importBlockBtn = document.getElementById('import-block-btn');
        const exportGridBtn = document.getElementById('export-grid-btn');
        const clearCanvasBtn = document.getElementById('clear-canvas-btn');
        const importFileInput = document.getElementById('import-file-input');
        const aiAssistantBtn = document.getElementById('ai-assistant-btn');
        const apiKeyInput = document.getElementById('api-key-input');
        const saveApiKeyBtn = document.getElementById('save-api-key-btn');
        const toggleCreateBtn = document.getElementById('toggle-create-view');
        const toggleManageBtn = document.getElementById('toggle-manage-view');
        const createView = document.getElementById('sidebar-view-create');
        const manageView = document.getElementById('sidebar-view-manage');


        // --- State Management ---
        let nodes = [];
        let edges = [];
        let nodeIdCounter = 0;
        let selectedNodeId = null;
        let isDragging = false;
        let isDrawingEdge = false;
        let startEdgeNode = null;
        let dragOffset = { x: 0, y: 0 };
        let aiChatHistory = [];

        // --- Node Definitions ---
        const nodeDefinitions = {
            Specification: {
                fields: {
                    format: ['select', ['Novel', 'Short Story', 'Screenplay', 'Bedtime Story', 'Play']],
                    genre: ['select', ['Sci-Fi', 'Fantasy', 'Mystery', 'Romance', 'Thriller', 'Horror', 'Comedy', 'Historical Fiction']],
                    audience: ['select', ['Children', 'Young Adult', 'Adults']]
                },
                preview: 'genre'
            },
            Character: { fields: { name: 'text', age: 'text', personality: 'textarea', backstory: 'textarea', motivation: 'textarea' }, preview: 'name' },
            Plot: { fields: { summary: 'textarea', conflict: 'textarea', resolution: 'textarea' }, preview: 'summary' },
            Setting: { fields: { location: 'text', time_period: 'text', atmosphere: 'textarea', sensory_details: 'textarea' }, preview: 'location' },
            Style: { fields: { prose_style: 'text', tone: 'text', narrative_perspective: 'text' }, preview: 'prose_style' },
            Theme: { fields: { central_idea: 'text', message: 'textarea' }, preview: 'central_idea' }
        };

        // --- Drag and Drop Logic ---
        libraryNodes.forEach(node => {
            node.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', e.target.closest('.library-node').dataset.type);
            });
        });
        canvasArea.addEventListener('dragover', (e) => e.preventDefault());
        canvasArea.addEventListener('drop', (e) => {
            e.preventDefault();
            const type = e.dataTransfer.getData('text/plain');
            const rect = canvasArea.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            if (type) createNode(type, x, y);
        });
        
        // --- Node Creation and Management ---
        function createNode(type, x, y, data = null, id = null) {
            const nodeId = id || `node-${nodeIdCounter++}`;
            const nodeData = {
                id: nodeId, type, x, y,
                data: data ? { ...data } : Object.keys(nodeDefinitions[type].fields).reduce((acc, key) => ({ ...acc, [key]: '' }), {})
            };
            nodes.push(nodeData);
            renderNode(nodeData);
            if (!data) selectNode(nodeId);
            return nodeId;
        }
        
        function deleteNode(nodeId) {
            nodes = nodes.filter(n => n.id !== nodeId);
            edges = edges.filter(e => e.from !== nodeId && e.to !== nodeId);
            document.getElementById(nodeId)?.remove();
            if(selectedNodeId === nodeId) hidePropertiesPanel();
            renderAllEdges();
        }

        // --- Rendering ---
        function renderNode(nodeData) {
            const nodeEl = document.createElement('div');
            nodeEl.id = nodeData.id;
            nodeEl.className = 'canvas-node';
            nodeEl.style.left = `${nodeData.x}px`;
            nodeEl.style.top = `${nodeData.y}px`;
            const typeClass = nodeData.type.toLowerCase();

            nodeEl.innerHTML = `
                <div class="node-header ${typeClass}-header">
                    <span>${nodeData.type}</span>
                    <button class="delete-node-btn" title="Delete Node">&times;</button>
                </div>
                <div class="node-content">Click to edit...</div>
                <div class="node-connector input" data-node-id="${nodeData.id}" data-type="input" title="Connect Input"></div>
                <div class="node-connector output" data-node-id="${nodeData.id}" data-type="output" title="Connect Output"></div>
            `;
            
            canvas.appendChild(nodeEl);
            updateNodeContentPreview(nodeData.id);

            nodeEl.addEventListener('mousedown', (e) => {
                e.stopPropagation();
                if (e.target.classList.contains('node-connector') || e.target.classList.contains('delete-node-btn')) return;
                selectNode(nodeData.id);
                isDragging = true;
                const node = nodes.find(n => n.id === nodeData.id);
                dragOffset.x = e.clientX - node.x;
                dragOffset.y = e.clientY - node.y;
            });
            
            nodeEl.querySelector('.delete-node-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                deleteNode(nodeData.id);
            });
            
            nodeEl.querySelector('.node-connector.output').addEventListener('mousedown', (e) => {
                e.stopPropagation();
                isDrawingEdge = true;
                startEdgeNode = nodes.find(n => n.id === e.currentTarget.dataset.nodeId);
                const tempLine = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                tempLine.id = 'temp-line';
                tempLine.setAttribute('stroke', 'white'); tempLine.setAttribute('stroke-width', '2'); tempLine.setAttribute('stroke-dasharray', '5,5'); tempLine.setAttribute('fill', 'none');
                connectorSvg.appendChild(tempLine);
            });
        }

        function updateNodeContentPreview(nodeId) {
            const node = nodes.find(n => n.id === nodeId);
            if (!node) return;
            const nodeEl = document.getElementById(nodeId);
            const contentEl = nodeEl.querySelector('.node-content');
            const previewField = nodeDefinitions[node.type].preview;
            const previewText = node.data[previewField];
            contentEl.textContent = previewText ? (previewText.length > 50 ? previewText.substring(0, 50) + '...' : previewText) : 'Click to edit...';
        }

        function selectNode(nodeId) {
            if (selectedNodeId && selectedNodeId !== nodeId) {
                document.getElementById(selectedNodeId)?.classList.remove('selected');
            }
            selectedNodeId = nodeId;
            document.getElementById(nodeId)?.classList.add('selected');
            renderPropertiesPanel();
        }
        
        function hidePropertiesPanel() {
            if (selectedNodeId) {
                 document.getElementById(selectedNodeId)?.classList.remove('selected');
            }
            selectedNodeId = null;
            propertiesPanel.classList.add('hidden');
        }

        function renderPropertiesPanel() {
            if (!selectedNodeId) {
                hidePropertiesPanel();
                return;
            }
            propertiesPanel.classList.remove('hidden');

            const node = nodes.find(n => n.id === selectedNodeId);
            if (!node) return;

            const definition = nodeDefinitions[node.type];
            let contentHTML = `
                <div class="properties-header">
                    <h2>${node.type} Properties</h2>
                    <button class="properties-close-btn" title="Close Properties">&times;</button>
                </div>
                 <div class="properties-load-section">
                    <label for="load-block-select">Load Saved ${node.type}:</label>
                    <div class="load-block-wrapper">
                         <select id="load-block-select" class="styled-select" data-type="${node.type}"></select>
                    </div>
                    <button id="manage-blocks-btn" class="secondary-btn" style="width: 100%; margin-top: 10px;">Manage Saved</button>
                </div>
                <form id="properties-form" onsubmit="return false;">`;
            
            for (const [key, fieldInfo] of Object.entries(definition.fields)) {
                const value = node.data[key] || '';
                const label = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
                contentHTML += `<div class="form-group"><label for="${key}">${label}</label>`;
                
                if (Array.isArray(fieldInfo) && fieldInfo[0] === 'select') {
                    contentHTML += `<select id="${key}" name="${key}" class="styled-select" data-node-id="${node.id}">`;
                    fieldInfo[1].forEach(option => {
                        contentHTML += `<option value="${option}" ${option === value ? 'selected' : ''}>${option}</option>`;
                    });
                    contentHTML += `</select>`;
                } else if (fieldInfo === 'textarea') {
                    contentHTML += `<textarea id="${key}" name="${key}" data-node-id="${node.id}">${value}</textarea>`;
                } else {
                    contentHTML += `<input type="text" id="${key}" name="${key}" value="${value}" data-node-id="${node.id}">`;
                }
                contentHTML += `</div>`;
            }
            contentHTML += `</form>
                <div class="properties-footer">
                    <button id="save-block-btn" class="secondary-btn">Save Block</button>
                    <button id="export-block-btn" class="secondary-btn">Export Block</button>
                </div>
            `;
            propertiesContent.innerHTML = contentHTML;
            
            const loadSelect = propertiesContent.querySelector('#load-block-select');
            populateSavedBlockSelect(loadSelect, node.type);
            loadSelect.addEventListener('change', (e) => {
                if (e.target.value) { loadBlock(node.type, e.target.value); e.target.value = ''; }
            });
            
            propertiesContent.querySelector('#manage-blocks-btn').addEventListener('click', () => showManagementModal('block', node.type));
            propertiesContent.querySelector('.properties-close-btn').addEventListener('click', hidePropertiesPanel);
            propertiesContent.querySelector('#save-block-btn').addEventListener('click', () => saveBlock(node));
            propertiesContent.querySelector('#export-block-btn').addEventListener('click', () => exportBlock(node));

            document.getElementById('properties-form').addEventListener('input', (e) => {
                const targetNode = nodes.find(n => n.id === e.target.dataset.nodeId);
                if (targetNode) {
                    targetNode.data[e.target.name] = e.target.value;
                    updateNodeContentPreview(targetNode.id);
                }
            });
        }
        
        canvasArea.addEventListener('mousedown', (e) => {
            if (e.target === canvasArea || e.target === canvas) {
                hidePropertiesPanel();
            }
        });
        
        // --- Mouse Movement & Edge Logic ---
        document.addEventListener('mousemove', (e) => {
            if (isDragging && selectedNodeId) {
                const node = nodes.find(n => n.id === selectedNodeId);
                const nodeEl = document.getElementById(selectedNodeId);
                if (node && nodeEl) {
                    node.x = e.clientX - dragOffset.x;
                    node.y = e.clientY - dragOffset.y;
                    nodeEl.style.left = `${node.x}px`;
                    nodeEl.style.top = `${node.y}px`;
                    renderAllEdges();
                }
            } else if (isDrawingEdge && startEdgeNode) {
                 const tempLine = document.getElementById('temp-line');
                 const startPos = getConnectorPosition(startEdgeNode.id, 'output');
                 const rect = canvasArea.getBoundingClientRect();
                 const endX = e.clientX - rect.left;
                 const endY = e.clientY - rect.top;
                 const pathData = `M ${startPos.x} ${startPos.y} C ${startPos.x + 100} ${startPos.y}, ${endX - 100} ${endY}, ${endX} ${endY}`;
                 if(tempLine) tempLine.setAttribute('d', pathData);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            if (isDrawingEdge) {
                isDrawingEdge = false;
                startEdgeNode = null;
                document.getElementById('temp-line')?.remove();
            }
        });
        
        canvas.addEventListener('mouseup', (e) => {
             if (isDrawingEdge && e.target.classList.contains('node-connector') && e.target.dataset.type === 'input') {
                const endNode = nodes.find(n => n.id === e.target.dataset.nodeId);
                if (startEdgeNode && endNode && startEdgeNode.id !== endNode.id) createEdge(startEdgeNode.id, endNode.id);
            }
        });
        
        function createEdge(fromId, toId) {
            if (!edges.some(e => e.from === fromId && e.to === toId)) {
                edges.push({ from: fromId, to: toId });
                renderAllEdges();
            }
        }

        function deleteEdge(fromId, toId) {
            edges = edges.filter(e => !(e.from === fromId && e.to === toId));
            renderAllEdges();
        }

        function getConnectorPosition(nodeId, type) {
            const nodeEl = document.getElementById(nodeId);
            if (!nodeEl) return { x: 0, y: 0 };
            const rect = nodeEl.getBoundingClientRect();
            const canvasRect = canvasArea.getBoundingClientRect();
            const x = (type === 'input' ? rect.left : rect.right) - canvasRect.left;
            const y = rect.top + rect.height / 2 - canvasRect.top;
            return { x, y };
        }

        function renderAllEdges() {
            connectorSvg.innerHTML = '';
            edges.forEach(edge => {
                const startPos = getConnectorPosition(edge.from, 'output');
                const endPos = getConnectorPosition(edge.to, 'input');
                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const pathData = `M ${startPos.x} ${startPos.y} C ${startPos.x + 100} ${startPos.y}, ${endPos.x - 100} ${endPos.y}, ${endPos.x} ${endPos.y}`;
                path.setAttribute('d', pathData);
                path.classList.add('connector-line');
                path.addEventListener('dblclick', () => deleteEdge(edge.from, edge.to));
                connectorSvg.appendChild(path);
            });
        }
        
        // --- Prompt Compilation & Generation ---
        function compileFinalPrompt() {
            const { orderedNodes, contextNodes } = traverseStoryGraph(nodes, edges);
            let promptParts = [];
            
            let finalPromptIntroduction = "You are a master storyteller. Create a coherent narrative by following the specified story flow and applying the general context. Weave the elements together naturally.";
            
            const specNode = nodes.find(n => n.type === 'Specification');
            if(specNode) {
                const { format, genre, audience } = specNode.data;
                if(format && genre && audience) {
                     finalPromptIntroduction = `You are a master storyteller. Create a coherent ${genre} ${format} for a(n) ${audience} audience. Follow the specified story flow and apply the general context. Weave the elements together naturally.`;
                }
            }
            promptParts.push(finalPromptIntroduction + "\n\n");

            if (contextNodes.length > 0) {
                promptParts.push("## GENERAL CONTEXT\nThese elements apply to the entire story:");
                contextNodes.forEach(node => {
                    if(node.type === 'Specification') return; // Don't repeat the spec node
                    let promptPart = `### ${node.type} Profile:\n`;
                    for (const [key, value] of Object.entries(node.data)) { if (value) promptPart += `- ${key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}: ${value}\n`; }
                    promptParts.push(promptPart);
                });
            }

            if (orderedNodes.length > 0) {
                promptParts.push("\n## STORY FLOW\nFollow this sequence of events to build the narrative:");
                orderedNodes.forEach((node, index) => {
                    let promptPart = `### Scene ${index + 1}: ${node.type}\n`;
                    for (const [key, value] of Object.entries(node.data)) { if (value) promptPart += `- ${key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}: ${value}\n`; }
                    promptParts.push(promptPart);
                });
            }
            return promptParts.join("\n");
        }

        function traverseStoryGraph(nodes, edges) {
            const nodeMap = new Map(nodes.map(node => [node.id, node]));
            const adjList = new Map(nodes.map(node => [node.id, []]));
            const inDegree = new Map(nodes.map(node => [node.id, 0]));
            for (const edge of edges) {
                if (adjList.has(edge.from)) adjList.get(edge.from).push(edge.to);
                if (inDegree.has(edge.to)) inDegree.set(edge.to, inDegree.get(edge.to) + 1);
            }
            const queue = nodes.filter(node => inDegree.get(node.id) === 0);
            const orderedNodes = [];
            const visited = new Set();
            while (queue.length > 0) {
                const node = queue.shift();
                if(visited.has(node.id)) continue;
                orderedNodes.push(node);
                visited.add(node.id);
                for (const neighborId of adjList.get(node.id) || []) {
                    inDegree.set(neighborId, inDegree.get(neighborId) - 1);
                    if (inDegree.get(neighborId) === 0) queue.push(nodeMap.get(neighborId));
                }
            }
            return { orderedNodes, contextNodes: nodes.filter(node => !visited.has(node.id)) };
        }

        generateBtn.addEventListener('click', async () => {
            if (nodes.length === 0) {
                 showModal(storyModal, `<p style="color: var(--error-color);">Please add at least one node to the canvas.</p>`);
                return;
            }
            generateBtn.disabled = true; generateBtn.textContent = 'Weaving...'; generateBtn.classList.add('loading');
            showModal(storyModal, '<div class="story-placeholder"><div class="loader"></div></div>');
            const finalPrompt = compileFinalPrompt();
            const apiKey = getApiKey(); 
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const payload = { contents: [{ role: "user", parts: [{ text: finalPrompt }] }], generationConfig: { temperature: 0.75, maxOutputTokens: 2048 } };
            try {
                const response = await fetch(apiUrl, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
                if (!response.ok) { 
                     if (response.status === 401) throw new Error("Authentication failed (401). Your API key is invalid or not configured. Please enter a valid key in the sidebar and save it.");
                    const errorData = await response.json(); throw new Error(errorData.error?.message || `HTTP error! status: ${response.status}`); 
                }
                const result = await response.json();
                if (result.candidates?.[0]?.content?.parts?.[0]?.text) { storyOutput.textContent = result.candidates[0].content.parts[0].text; }
                else {
                    let errorMessage = 'Invalid or empty response from API.';
                    if (result.candidates?.[0]?.finishReason) { errorMessage = `Story generation stopped. Reason: ${result.candidates[0].finishReason}.`; if (result.candidates[0].finishReason === 'SAFETY') errorMessage += ' Please adjust your prompt content.'; }
                    else if (result.error) errorMessage = `API Error: ${result.error.message}`;
                    throw new Error(errorMessage);
                }
            } catch(error) {
                storyOutput.innerHTML = `<p style="color: var(--error-color);"><strong>Error:</strong> ${error.message}</p>`;
                console.error('Generation Error:', error);
            } finally {
                generateBtn.disabled = false; generateBtn.textContent = 'Weave Story'; generateBtn.classList.remove('loading');
            }
        });
        
        // --- Save/Load/Import/Export Logic ---
        const STORAGE_KEYS = { GRIDS: 'storyWeaver_savedGrids', BLOCKS_PREFIX: 'storyWeaver_savedBlocks_', API_KEY: 'storyWeaver_apiKey' };

        function saveBlock(node) {
            showPromptModal(`Enter a name for this ${node.type} block:`, (blockName) => {
                const storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${node.type}`;
                const savedBlocks = JSON.parse(localStorage.getItem(storageKey) || '{}');
                savedBlocks[blockName] = node.data;
                localStorage.setItem(storageKey, JSON.stringify(savedBlocks));
                renderPropertiesPanel();
            });
        }

        function loadBlock(type, blockName) {
            if (!selectedNodeId) return;
            const node = nodes.find(n => n.id === selectedNodeId);
            if (!node || node.type !== type) return;
            const storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${type}`;
            const savedBlocks = JSON.parse(localStorage.getItem(storageKey) || '{}');
            const blockData = savedBlocks[blockName];
            if (blockData) { node.data = { ...blockData }; renderPropertiesPanel(); updateNodeContentPreview(node.id); }
        }

        function populateSavedBlockSelect(select, type) {
            const storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${type}`;
            const savedBlocks = JSON.parse(localStorage.getItem(storageKey) || '{}');
            select.innerHTML = `<option value="">Load Saved ${type}...</option>`;
            for (const blockName in savedBlocks) { const option = document.createElement('option'); option.value = blockName; option.textContent = blockName; select.appendChild(option); }
        }
        
        function exportBlock(node) {
            const dataToExport = { type: `story-weaver-block`, blockType: node.type, data: node.data };
            const filename = `${node.type.toLowerCase()}_${node.data.name?.replace(/\s+/g, '_') || 'export'}.json`;
            downloadJSON(dataToExport, filename);
        }

        function saveGrid() {
            showPromptModal("Enter a name for this grid:", (gridName) => {
                const savedGrids = JSON.parse(localStorage.getItem(STORAGE_KEYS.GRIDS) || '{}');
                const gridData = { nodes: nodes, edges: edges };
                savedGrids[gridName] = gridData;
                localStorage.setItem(STORAGE_KEYS.GRIDS, JSON.stringify(savedGrids));
                populateGridSelect();
            });
        }
        
        function loadGrid(gridName) {
            const savedGrids = JSON.parse(localStorage.getItem(STORAGE_KEYS.GRIDS) || '{}');
            const demoGrids = getDemoData();
            const gridData = savedGrids[gridName] || demoGrids[gridName];
            if (gridData) loadGridFromData(gridData);
        }

        function populateGridSelect() {
            const savedGrids = JSON.parse(localStorage.getItem(STORAGE_KEYS.GRIDS) || '{}');
            const demoGrids = getDemoData();
            gridSelect.innerHTML = `<option value="">Load a Grid...</option>`;
            const demoOptGroup = document.createElement('optgroup');
            demoOptGroup.label = 'Demos';
            for (const name in demoGrids) { const option = document.createElement('option'); option.value = name; option.textContent = name; demoOptGroup.appendChild(option); }
            gridSelect.appendChild(demoOptGroup);
            if (Object.keys(savedGrids).length > 0) {
                const userOptGroup = document.createElement('optgroup');
                userOptGroup.label = 'My Saved Grids';
                for (const name in savedGrids) { const option = document.createElement('option'); option.value = name; option.textContent = name; userOptGroup.appendChild(option); }
                gridSelect.appendChild(userOptGroup);
            }
        }
        
        gridSelect.addEventListener('change', (e) => {
            if (e.target.value) { loadGrid(e.target.value); e.target.value = ""; }
        });
        
        function exportGrid() {
            if (nodes.length === 0) { showConfirmModal("Canvas is empty. Nothing to export.", ()=>{}); return; }
            downloadJSON({ type: 'story-weaver-grid', nodes, edges }, 'story-grid.json');
        }

        function importFile(file) {
             const reader = new FileReader();
             reader.onload = (event) => {
                try {
                    const data = JSON.parse(event.target.result);
                    if (data.type === 'story-weaver-grid' && data.nodes && data.edges) {
                        loadGridFromData(data);
                    } else if (data.type === 'story-weaver-block' && data.blockType && data.data) {
                        importBlockFromFileData(data);
                    } else {
                         throw new Error("Invalid or corrupted file format.");
                    }
                } catch (e) { showConfirmModal(`Error importing file: ${e.message}`, ()=>{}); }
             };
             reader.readAsText(file);
        }
        
        function importBlockFromFileData(data) {
            showPromptModal(`Enter a name for this imported ${data.blockType} block:`, (blockName) => {
                const storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${data.blockType}`;
                const savedBlocks = JSON.parse(localStorage.getItem(storageKey) || '{}');
                savedBlocks[blockName] = data.data;
                localStorage.setItem(storageKey, JSON.stringify(savedBlocks));
                if (selectedNodeId) renderPropertiesPanel(); // Refresh panel if open
                showConfirmModal(`Block "${blockName}" imported successfully!`, ()=>{});
            });
        }

        function loadGridFromData(gridData) {
            clearCanvas();
            nodeIdCounter = 0;
            const maxId = gridData.nodes.reduce((max, node) => {
                const num = parseInt(node.id.split('-')[1]);
                return isNaN(num) ? max : Math.max(max, num);
            }, -1);
            nodeIdCounter = maxId + 1;

            gridData.nodes.forEach(node => {
                createNode(node.type, node.x, node.y, node.data, node.id);
            });
            edges = gridData.edges ? gridData.edges.map(e => ({...e})) : [];
            renderAllEdges();
            hidePropertiesPanel();
        }
        
        importGridBtn.addEventListener('click', () => importFileInput.click());
        importBlockBtn.addEventListener('click', () => importFileInput.click());
        importFileInput.addEventListener('change', (e) => { if (e.target.files[0]) importFile(e.target.files[0]); });

        // --- Utility Functions ---
        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url; a.download = filename;
            document.body.appendChild(a); a.click(); document.body.removeChild(a); URL.revokeObjectURL(url);
        }

        function clearCanvas() {
            nodes = []; edges = []; canvas.innerHTML = ''; connectorSvg.innerHTML = ''; nodeIdCounter = 0; hidePropertiesPanel();
        }

        // --- UI & Modal Controls ---
        function showModal(modalElement, content) {
            if (modalElement === storyModal) storyOutput.innerHTML = content;
            else promptOutput.textContent = content;
            modalElement.classList.add('visible');
        }

        function copyToClipboard(text, button) {
            if(!text) return;
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!'; button.style.backgroundColor = 'var(--success-color)';
                setTimeout(() => { button.textContent = originalText; button.style.backgroundColor = ''; }, 2000);
            }).catch(err => console.error('Could not copy text: ', err));
        }
        
        function showPromptModal(text, onConfirm) {
            const modal = document.getElementById('generic-prompt-modal');
            modal.classList.add('top');
            modal.querySelector('#generic-prompt-modal-text').textContent = text;
            const input = modal.querySelector('#generic-prompt-modal-input');
            input.value = '';

            const confirmBtn = modal.querySelector('#generic-prompt-modal-confirm');
            const cancelBtn = modal.querySelector('#generic-prompt-modal-cancel');
            
            const closeHandler = () => {
                modal.classList.remove('visible');
                modal.classList.remove('top');
                confirmBtn.removeEventListener('click', confirmHandler);
                cancelBtn.removeEventListener('click', closeHandler);
            };
            const confirmHandler = () => {
                if (input.value) {
                    onConfirm(input.value);
                    closeHandler();
                }
            };
            
            confirmBtn.addEventListener('click', confirmHandler);
            cancelBtn.addEventListener('click', closeHandler);
            modal.querySelector('.modal-close-btn').addEventListener('click', closeHandler);
            modal.classList.add('visible');
            input.focus();
        }

        function showConfirmModal(text, onConfirm) {
            const modal = document.getElementById('generic-confirm-modal');
            modal.classList.add('top');
            modal.querySelector('#generic-confirm-modal-text').textContent = text;
            const confirmBtn = modal.querySelector('#generic-confirm-modal-confirm');
            const cancelBtn = modal.querySelector('#generic-confirm-modal-cancel');

            const closeHandler = () => {
                modal.classList.remove('visible');
                modal.classList.remove('top');
                confirmBtn.removeEventListener('click', confirmHandler);
                cancelBtn.removeEventListener('click', closeHandler);
            };
            const confirmHandler = () => {
                onConfirm();
                closeHandler();
            };

            confirmBtn.addEventListener('click', confirmHandler);
            cancelBtn.addEventListener('click', closeHandler);
            modal.querySelector('.modal-close-btn').addEventListener('click', closeHandler);
            modal.classList.add('visible');
        }
        
        function showManagementModal(itemType, blockType) {
            const modal = document.getElementById('management-modal');
            modal.classList.add('top');
            const listEl = modal.querySelector('#management-list');
            const titleEl = modal.querySelector('#management-modal-title');
            
            let storageKey, items;
            if(itemType === 'grid') {
                titleEl.textContent = "Manage Saved Grids";
                storageKey = STORAGE_KEYS.GRIDS;
                items = JSON.parse(localStorage.getItem(storageKey) || '{}');
            } else {
                titleEl.textContent = `Manage Saved ${blockType}s`;
                storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${blockType}`;
                items = JSON.parse(localStorage.getItem(storageKey) || '{}');
            }
            
            listEl.innerHTML = '';
            if(Object.keys(items).length === 0) {
                listEl.innerHTML = '<p style="text-align: center; color: var(--subtle-text-color);">No items saved yet.</p>';
            }

            for (const name in items) {
                const itemEl = document.createElement('div');
                itemEl.className = 'management-item';
                itemEl.innerHTML = `<span>${name}</span>`;
                const deleteBtn = document.createElement('button');
                deleteBtn.textContent = 'Delete';
                deleteBtn.className = 'modal-btn danger';
                deleteBtn.onclick = () => {
                    showConfirmModal(`Are you sure you want to delete "${name}"? This cannot be undone.`, () => {
                        delete items[name];
                        localStorage.setItem(storageKey, JSON.stringify(items));
                        showManagementModal(itemType, blockType); // Refresh the list
                        if(itemType === 'grid') populateGridSelect();
                        else renderPropertiesPanel();
                    });
                };
                itemEl.appendChild(deleteBtn);
                listEl.appendChild(itemEl);
            }
            
            modal.classList.add('visible');
        }


        copyStoryBtn.addEventListener('click', () => copyToClipboard(storyOutput.textContent, copyStoryBtn));
        copyPromptBtn.addEventListener('click', () => copyToClipboard(promptOutput.textContent, copyPromptBtn));
        
        viewPromptBtn.addEventListener('click', () => {
            if (nodes.length === 0) { showModal(promptModal, 'Add nodes to see the compiled prompt.'); return; }
            showModal(promptModal, compileFinalPrompt());
        });
        
        [storyModal, promptModal, document.getElementById('generic-prompt-modal'), document.getElementById('generic-confirm-modal'), aiAssistantModal, managementModal].forEach(modal => {
            modal.addEventListener('click', (e) => { if(e.target === modal) modal.classList.remove('visible'); });
            modal.querySelector('.modal-close-btn').addEventListener('click', () => modal.classList.remove('visible'));
        });
        
        clearCanvasBtn.addEventListener('click', () => {
            showConfirmModal("Are you sure you want to clear the entire canvas?", () => {
                clearCanvas();
                gridSelect.value = "";
            });
        });

        saveGridBtn.addEventListener('click', saveGrid);
        manageGridsBtn.addEventListener('click', () => showManagementModal('grid'));
        exportGridBtn.addEventListener('click', exportGrid);
        
        toggleCreateBtn.addEventListener('click', () => {
            createView.classList.remove('hidden');
            manageView.classList.add('hidden');
            toggleCreateBtn.classList.add('active');
            toggleManageBtn.classList.remove('active');
        });
        toggleManageBtn.addEventListener('click', () => {
            createView.classList.add('hidden');
            manageView.classList.remove('hidden');
            toggleCreateBtn.classList.remove('active');
            toggleManageBtn.classList.add('active');
        });

        // --- AI Assistant Logic ---
        aiAssistantBtn.addEventListener('click', () => {
            aiAssistantModal.classList.add('visible');
            if(chatHistory.children.length === 0) {
                 addMessageToChat("Hello! I'm your AI Story Assistant. How can I help you build your narrative? You can ask me to create characters, plots, settings, and more.", 'bot');
            }
        });
        
        chatInput.addEventListener('input', () => {
            chatInput.style.height = 'auto';
            chatInput.style.height = (chatInput.scrollHeight) + 'px';
        });

        chatInputForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const userInput = chatInput.value.trim();
            if (userInput) {
                addMessageToChat(userInput, 'user');
                aiChatHistory.push({ role: "user", parts: [{ text: userInput }] });
                callAIAssistant();
                chatInput.value = '';
                 chatInput.style.height = 'auto';
            }
        });
        
        function addMessageToChat(text, sender) {
            const messageEl = document.createElement('div');
            messageEl.className = `chat-message ${sender}`;
            
            if (sender === 'bot' && text === 'loading') {
                messageEl.classList.add('loading');
                messageEl.innerHTML = `<div class="dot"></div><div class="dot"></div><div class="dot"></div>`;
            } else {
                 messageEl.textContent = text;
            }
            
            chatHistory.appendChild(messageEl);
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        async function callAIAssistant() {
            addMessageToChat('loading', 'bot');

            const systemInstruction = `You are an AI assistant for a story-building application. Your primary function is to interpret a user's request and generate a valid JSON object representing one or more "story blocks" to be used in the application.

            **Critical Rules:**
            1.  **JSON Only:** You MUST ONLY respond with a single, valid JSON object. Do not include any conversational text, explanations, apologies, or markdown formatting like \`\`\`json in your response.
            2.  **Strict Schema Adherence:** The JSON object must strictly adhere to the provided schema.
            3.  **Complete Blocks:** For each block you create, you MUST populate all relevant fields to create a complete, coherent block. For example, a "Plot" block is incomplete without a "summary", a "conflict", AND a "resolution". A "Theme" block is not complete without a "central_idea" AND a "message". IT IS A REQUIREMENT TO FILL EVERY FIELD FOR A BLOCK.
            
            **Schema Definition:**
            - The root object must have one key: "blocks".
            - "blocks" is an array of block objects.
            - Each block object has the following properties:
              - "blockType": (string) The type of block. Must be one of: "Character", "Plot", "Setting", "Style", "Theme", "Specification".
              - "blockName": (string) A short, descriptive title for the block.
              - "data": (object) A JSON object containing the block's data, with keys matching the required fields for that blockType.

            **Example Request:**
            "Create a cynical detective character named Miles Corbin."

            **Example CORRECT Response:**
            {
              "blocks": [
                {
                  "blockType": "Character",
                  "blockName": "Cynical Detective Corbin",
                  "data": {
                    "name": "Miles Corbin",
                    "age": "45",
                    "personality": "World-weary and jaded, but with a strong, if buried, sense of justice.",
                    "backstory": "A brilliant detective who was disgraced and now works as a private investigator on the fringes of society.",
                    "motivation": "To find redemption by solving one last, difficult case."
                  }
                }
              ]
            }`;

            const payload = {
              contents: aiChatHistory,
              systemInstruction: {
                parts: [{ text: systemInstruction }]
              },
              generationConfig: {
                responseMimeType: "application/json",
                responseSchema: {
                  type: "OBJECT",
                  properties: {
                    blocks: {
                      type: "ARRAY",
                      items: {
                        type: "OBJECT",
                        properties: {
                          blockType: { "type": "STRING" },
                          blockName: { "type": "STRING" },
                          data: { "type": "OBJECT", properties: {} }
                        },
                        required: ["blockType", "blockName", "data"]
                      }
                    }
                  }
                }
              }
            };
            
            const apiKey = getApiKey();
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    if (response.status === 401) {
                         throw new Error("Authentication failed (401). Your API key is invalid or not configured. Please enter a valid key in the sidebar and save it.");
                    }
                    const errorData = await response.json().catch(() => null);
                    const errorMessage = errorData?.error?.message || `API Error: ${response.statusText} (${response.status})`;
                    throw new Error(errorMessage);
                }

                const result = await response.json();
                
                if (!result.candidates?.[0]?.content?.parts?.[0]?.text) {
                    throw new Error("Received an invalid response from the AI assistant.");
                }

                const jsonResponse = JSON.parse(result.candidates[0].content.parts[0].text);
                aiChatHistory.push({role: "model", parts: [{text: JSON.stringify(jsonResponse)}]});
                
                const { createdBlockNames, gridLoaded } = processAIActions(jsonResponse);
                
                let botResponseText = "I couldn't create any blocks from your request. Please try rephrasing.";
                if (gridLoaded) {
                    botResponseText = "I've created and loaded the new story grid onto your canvas. The individual blocks have also been saved to your library.";
                } else if (createdBlockNames.length > 0) {
                     botResponseText = `I've created ${createdBlockNames.length} new block(s) for you: ${createdBlockNames.join(', ')}. You can load them from the Properties Panel when a node of the correct type is selected.`;
                }

                const loadingIndicator = chatHistory.querySelector('.loading');
                if (loadingIndicator) loadingIndicator.remove();
                addMessageToChat(botResponseText, 'bot');

            } catch (error) {
                 const loadingIndicator = chatHistory.querySelector('.loading');
                if (loadingIndicator) loadingIndicator.remove();
                addMessageToChat(`Sorry, I ran into an error: ${error.message}`, 'bot');
                console.error("AI Assistant Error:", error);
            }
        }
        
        function saveAiGeneratedBlock(blockType, blockName, data) {
            const storageKey = `${STORAGE_KEYS.BLOCKS_PREFIX}${blockType}`;
            const savedBlocks = JSON.parse(localStorage.getItem(storageKey) || '{}');
            savedBlocks[blockName] = data;
            localStorage.setItem(storageKey, JSON.stringify(savedBlocks));
        }

        function processAIActions(response) {
            let createdBlockNames = [];
            let gridLoaded = false;
            
            if (response.blocks && Array.isArray(response.blocks)) {
                
                // If more than one block is returned, assume it's a grid request
                if (response.blocks.length > 1) {
                    clearCanvas();
                    const plotNodes = [];
                    const otherNodes = [];

                    response.blocks.forEach(block => {
                        saveAiGeneratedBlock(block.blockType, block.blockName, block.data);
                        createdBlockNames.push(`'${block.blockName}' (${block.blockType})`);
                        if (block.blockType === 'Plot') {
                            plotNodes.push(block);
                        } else {
                            otherNodes.push(block);
                        }
                    });

                    const allNodesToLoad = [...plotNodes, ...otherNodes];
                    const createdNodeIds = new Map();
                    let yPos = 50;
                    let otherNodeIndex = 0;

                    allNodesToLoad.forEach((nodeInfo) => {
                         const xPos = nodeInfo.blockType === 'Plot' ? 320 : (otherNodeIndex++ % 2 === 0 ? 50 : 600);
                         const nodeId = createNode(nodeInfo.blockType, xPos, yPos, nodeInfo.data);
                         createdNodeIds.set(nodeInfo.blockName, nodeId);
                         if (nodeInfo.blockType === 'Plot') yPos += 180;
                    });
                    
                    const createdPlotNodes = plotNodes.map(n => createdNodeIds.get(n.blockName));
                    for(let i = 0; i < createdPlotNodes.length - 1; i++){
                        createEdge(createdPlotNodes[i], createdPlotNodes[i+1]);
                    }
                    
                    gridLoaded = true;

                } else { // Handle single block creation
                     response.blocks.forEach(action => {
                        try {
                            const { blockType, blockName, data } = action;
                            saveAiGeneratedBlock(blockType, blockName, data);
                            createdBlockNames.push(`'${blockName}' (${blockType})`);
                        } catch(e) {
                             console.error("Failed to process AI action:", action, e);
                        }
                     });
                }
            }
            if (selectedNodeId) renderPropertiesPanel();
            return { createdBlockNames, gridLoaded };
        }

        // --- Initial Setup ---
        function getApiKey() {
            return localStorage.getItem(STORAGE_KEYS.API_KEY) || "";
        }

        function loadApiKey() {
            apiKeyInput.value = getApiKey();
        }
        saveApiKeyBtn.addEventListener('click', () => {
            localStorage.setItem(STORAGE_KEYS.API_KEY, apiKeyInput.value.trim());
            showConfirmModal("API Key Saved!", ()=>{});
        });

        function getDemoData() {
            return {
                '1. Comprehensive Story': { nodes: [ { id: 'node-0', type: 'Plot', x: 320, y: 50, data: { summary: 'A rain-slicked street at midnight. Detective Corbin is called to a crime scene at a waterfront warehouse.', conflict: 'The victim is a high-profile city official. A single, enigmatic clue is left behind: a silver locket.', resolution: 'Corbin takes the case, and the locket, knowing it will lead him into the city\'s corrupt underbelly.' }}, { id: 'node-1', type: 'Plot', x: 320, y: 280, data: { summary: 'Corbin meets with his informant, Lila, at a smoky jazz club.', conflict: 'Lila recognizes the locket. She is terrified, warning Corbin to drop the case, saying it\'s connected to a powerful, untouchable figure.', resolution: 'Lila gives Corbin a cryptic name and address before disappearing into the crowd.' }}, { id: 'node-2', type: 'Character', x: 50, y: 50, data: { name: 'Detective Miles Corbin', age: '45', personality: 'Cynical, world-weary, with a hidden sense of justice.', backstory: 'A former star detective disgraced after a case went wrong.', motivation: 'To solve one last case and find redemption.' }}, { id: 'node-3', type: 'Character', x: 50, y: 280, data: { name: 'Lila "The Whisper" Vance', age: '30', personality: 'Mysterious, nervous, and opportunistic.', backstory: 'An informant who survives by trading secrets.', motivation: 'Self-preservation at all costs.' }}, { id: 'node-4', type: 'Style', x: 600, y: 50, data: { prose_style: 'Hard-boiled, minimalist, with sharp, punchy sentences.', tone: 'Film Noir. Pessimistic and cynical.', narrative_perspective: 'Third Person Limited (following Corbin).' }}, { id: 'node-5', type: 'Theme', x: 600, y: 170, data: { central_idea: 'The corrupting influence of power.', message: 'In a city without heroes, a flawed man seeks a personal form of justice.' }}, {id: 'node-6', type: 'Setting', x: 600, y: 290, data: { location: 'A rain-drenched, unnamed metropolis.', time_period: 'A perpetual, noir-ish 1940s night.', atmosphere: 'Oppressive, shadowy, and dangerous.', sensory_details: 'The constant smell of wet asphalt and cheap cigarettes. The distant wail of a police siren.'}}, {id: 'node-7', type: 'Specification', x: 320, y: 460, data: { format: 'Novel', genre: 'Mystery', audience: 'Adults'}} ], edges: [{ from: 'node-0', to: 'node-1' }] },
                '2. Scoped Style (Flashback)': { nodes: [ { id: 'node-0', type: 'Plot', x: 320, y: 50, data: { summary: 'Corbin stares at the locket in his office, the rain outside matching his mood.', conflict: 'He opens it. The picture inside triggers a memory he had long suppressed.', resolution: 'The world fades away as he is pulled back into the past.' }}, { id: 'node-1', type: 'Plot', x: 320, y: 280, data: { summary: 'FLASHBACK: A sunny day years ago. A younger, happier Corbin is at a park with someone important.', conflict: 'This memory is tied to the locket and the origins of his disgrace.', resolution: 'The memory reveals a key detail about the locket he never realized before.' }}, { id: 'node-2', type: 'Plot', x: 320, y: 510, data: { summary: 'Corbin slams back to reality, the locket feeling heavier in his hand.', conflict: 'The new information re-contextualizes the current case completely.', resolution: 'He now knows his first stop must be to see Lila.' }}, { id: 'node-3', type: 'Character', x: 50, y: 50, data: { name: 'Detective Miles Corbin', age: '45', personality: 'Cynical, world-weary, but with a hidden sense of justice.', backstory: 'A former star detective disgraced after a case went wrong.', motivation: 'To solve one last case and find redemption.' }}, { id: 'node-4', type: 'Style', x: 600, y: 50, data: { prose_style: 'Hard-boiled, minimalist.', tone: 'Film Noir. Pessimistic and cynical.', narrative_perspective: 'Third Person Limited (following Corbin).' }}, { id: 'node-5', type: 'Style', x: 600, y: 280, data: { prose_style: 'Softer, more descriptive.', tone: 'Nostalgic, hazy, dreamlike, sepia-toned.', narrative_perspective: 'Third Person Limited (following a younger Corbin).' }} ], edges: [ { from: 'node-0', to: 'node-1' }, { from: 'node-1', to: 'node-2' }, { from: 'node-5', to: 'node-1' } ] },
                '3. Scoped Setting (Location Change)': { nodes: [ { id: 'node-0', type: 'Plot', x: 320, y: 50, data: { summary: 'Corbin reviews the case file under the dim light of his desk lamp.', conflict: 'The details don\'t add up. He realizes he needs information only an insider would have.', resolution: 'He decides to visit his reluctant informant, Lila.' }}, { id: 'node-1', type: 'Plot', x: 320, y: 280, data: { summary: 'Corbin finds Lila at her usual booth at "The Blue Dahlia".', conflict: 'He presents the case details. Lila is hesitant to talk, fearing repercussions.', resolution: 'After some convincing, she agrees to help, but for a steep price.' }}, { id: 'node-2', type: 'Character', x: 50, y: 50, data: { name: 'Detective Miles Corbin', age: '45', personality: 'Cynical, world-weary.', motivation: 'To solve one last case.' }}, { id: 'node-3', type: 'Character', x: 50, y: 280, data: { name: 'Lila "The Whisper" Vance', age: '30', personality: 'Mysterious, nervous, and opportunistic.', motivation: 'Self-preservation.' }}, { id: 'node-4', type: 'Setting', x: 600, y: 50, data: { location: 'Corbin\'s cluttered, dusty office.', time_period: 'A perpetual, rainy night in the 1940s.', atmosphere: 'Lonely and claustrophobic.', sensory_details: 'The smell of stale coffee and old paper. The constant drumming of rain on the window.' }}, { id: 'node-5', type: 'Setting', x: 600, y: 280, data: { location: 'The Blue Dahlia jazz club.', atmosphere: 'Dimly lit, filled with secrets and whispers.', sensory_details: 'The air is thick with cigarette smoke. A mournful saxophone wails on stage. The clinking of glasses.' }} ], edges: [ { from: 'node-0', to: 'node-1' }, { from: 'node-5', to: 'node-1' } ] }
            };
        }
        
        loadApiKey();
        populateGridSelect();

    });
    </script>
</body>
</html>
